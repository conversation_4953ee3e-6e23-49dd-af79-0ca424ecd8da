
  <div class="ifp-container">
    <app-ifp-breadcrumbs [pageData]="pageData()"></app-ifp-breadcrumbs>
  </div>
<div class="ifp-dxp" [ngClass]="{'ifp-dxp--catalog': selectedTabView().name === 'DXP Catalogue'}">
  <div class="ifp-container">
    <div class="ifp-dxp__header-wrapper ifp-dxp__header-wrapper--main">
      <h1 class="ifp-dxp__header">
        {{'Bayaan Insights Hub' | translate}}
      </h1>
      <ifp-search class="ifp-dxp__search" [isOnlyBorder]="true" [isKeypress]="true" (searchEvent)="searchResult($event)"
      [onSearch]="searchString()" [boxType]="true" [placeholderText]="'Search'| translate"></ifp-search>

    </div>

      <div class="ifp-dxp__header-wrapper ifp-dxp__header-wrapper--sub">
      <app-ifp-tab [tabData]="dashboardTabs()"  (selectedTabEvent)="changeTabView($event)"
      [selectionType]="'name'" [selectedTab]="selectedTabView().name" [tooltipDisabled]="true" class="ifp-dxp__tab"></app-ifp-tab>
      @if (selectedTabView().name !== 'DXP Catalogue') {
        <div class="ifp-dxp__btn-wrapper">
          <ifp-button  class="ifp-dxp__btn" [label]="'Delete'" [iconClass]="'ifp-icon-trash'" [buttonClass]="buttonClass.disabled"></ifp-button>
          <ifp-button  class="ifp-dxp__btn" [label]="'Create KPI’s'" [iconClass]="'ifp-icon-plus-light'" [buttonClass]="buttonClass.primaryLight" (ifpClick)="gotToKpiPage()"></ifp-button>
        </div>
        }
    </div>
    <p class="ifp-dxp__desc">{{sideMenu()[subTabSelected].description}}</p>
    <div class="ifp-dxp__section-wrapper">
      @if (selectedTabView().name !== 'DXP Catalogue') {
      <div class="ifp-dxp__left">
        <app-ifp-dropdown  [boarderBottom]="true" [selectedValue]="" [checkBoxKey]="'domain'"  [singleDefaultSelect]='false' [key]="'name'" [isMulti]="false" class="ifp-dxp__dropdown" [placeHolder]="'Select Entity'" [dropDownItems]="entity()"></app-ifp-dropdown>

        <ifp-horizontal-tab [activeClass]="'ifp-h-tab--primary-light'" [iconClass]="'ifp-icon-rightarrow'" [showCount]="true" [disableTooltip]="true" [(selected)]="subTabSelected" [list]="sideMenu()" class="ifp-dxp__htab" (selectionClick)="selectSideBar($event)" ></ifp-horizontal-tab>
      </div>
      }
     <div class="ifp-dxp__right">
        <router-outlet></router-outlet>
     </div>
    </div>
  </div>


</div>



