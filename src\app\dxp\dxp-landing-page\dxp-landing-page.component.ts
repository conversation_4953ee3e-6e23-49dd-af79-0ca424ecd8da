import { Component,  inject,  <PERSON><PERSON><PERSON>roy,  OnInit,  signal  } from '@angular/core';

import { LabelData } from 'src/app/scad-insights/core/interface/atom/ifp-category-label.interface';
import { buttonClass } from '../../scad-insights/core/constants/button.constants';
import { IfpHorizontalTabComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-horizontal-tab/ifp-horizontal-tab.component';
import { Router, RouterOutlet, ActivatedRoute } from '@angular/router';

import { IfpButtonComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpBreadcrumbsComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpTabComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { IfpSearchComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component';
import { IfpDropdownComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component";
import { NgClass } from '@angular/common';
import { SubSink } from 'subsink';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { dxpApi } from '../dxp.constants';
import { ListingPageData, ListingPageItem } from '../dxp.interface';

@Component({
  selector: 'ifp-dxp-landing-page',
  imports: [IfpBreadcrumbsComponent,
    TranslateModule,
    IfpTabComponent,
    IfpSearchComponent,
    IfpButtonComponent,
    IfpHorizontalTabComponent,
    RouterOutlet, IfpDropdownComponent, NgClass],
  templateUrl: './dxp-landing-page.component.html',
  styleUrl: './dxp-landing-page.component.scss',
})
export class DxpLandingPageComponent implements OnInit, OnDestroy {
  public _router = inject(Router);
  public _apiService = inject(ApiService);
  public searchString = signal('');
  public pageData = signal([
    {
      title: 'Home',
      route: '/home',
    },
    {
      title: 'Abu Dhabi Executive Office (ADEO)',
      route: '',
    },
  ]);
  public dashboardTabs = signal<LabelData[]>([
    {
      name: 'KPIs',
      key: 'kpis',
      iconClass: 'ifp-icon-kpi',
      route: '/dxp'
    },
    {
      name: 'Dashboards',
      key: 'dashboards',
      iconClass: 'ifp-icon-desktop-chart',
      route: '/dxp'
    },
    {
      name: 'DXP Catalogue',
      key: 'catalogue',
      iconClass: 'ifp-icon-catelogue',
      route: '/dxp/catalogue'
    },
  ]);

  public subs: SubSink = new SubSink();
  public subTabSelected = 0;
  public sideMenu =  signal([{
    name: 'KPIs',
    description: 'This page displays a list of KPIs associated with a specific entity, providing key performance insights and relevant metadata. Users can view, filter, and manage KPIs linked to the selected entity.',
  },
  {
    name: 'Drafts',
    description: ''
  },
  {
    name: 'New Request',
    description: 'This page lists all new visualizations linked to your entity. You can review each submission and choose to approve, reject, or revert it with a message to the user if changes are needed.',
    value: 10
  },
  {
    name: 'Pending',
    description: ''
  },
  {
    name: 'Existing',
    description: ''
  },
  {
    name: 'Unpublished',
    description: ''
  },
]);
  public buttonClass = buttonClass;
  public entity = signal([]);
  public selectedTabView = signal<LabelData>({ name: 'KPIs', key: 'kpi'});
  public _route = inject(ActivatedRoute);
  public sideMenuStatus:string = '';
  public ListingPageData: ListingPageData | null = null;
  private readonly _subs: SubSink = new SubSink();
  public dxpListingData: ListingPageItem[] = [];

  constructor() {
    this.subs.add(
      this._router.events.subscribe({
        next: ()=> {
          const tabIndex = this.dashboardTabs().findIndex((item: LabelData) => item.route === this._router.url);
          this.selectedTabView.set(tabIndex >= 0 ? this.dashboardTabs()[tabIndex] : this.dashboardTabs()[0]);
        }
      })
    )
  }

  ngOnInit(): void {
    this.getEntityList();
    this._route.queryParams.subscribe(params => {
      if (params['subTab']) {
        this.subTabSelected = +params['subTab'];
        this.sideMenuStatus ='pending';
        this.getIndicatorList(this.sideMenuStatus);
        this.getIndicatorList('new-requests');
      }
    });
  }

  getIndicatorList(val?:string) {
    // if(val === 'pending'){
      this._subs.add(
        this._apiService.getMethodRequest(dxpApi.listEntityKpi, { status:val?val: 'pending' }).subscribe({
          next: (resp: ListingPageData) => {
            this.dxpListingData = resp?.data ?? [];

            // Update the sideMenu by adding value key to the pending item
            const currentSideMenu = this.sideMenu();
            const updatedSideMenu = currentSideMenu.map(item => {
              if (item.name === 'Pending') {
                return {
                  ...item,
                  value: resp?.totalCount || 0
                };
              }else if(item.name === 'new-requests'){
                return {
                  ...item,
                  value: resp?.totalCount || 0
                };
              }
              return item;
            });
            this.sideMenu.set(updatedSideMenu);
          }
        })
      );
    // }
  }

  getEntityList() {
    this.subs.add(
      this._apiService
        .getMethodRequest(dxpApi.listEntityKpi, { status: 'pending' })
        .subscribe({
          next: (resp: ListingPageData) => {
            this.ListingPageData = resp;
            console.log(resp);
          },
        })
    );
  }

  searchResult(value: string) {
    this.searchString.set(value);
  }
  selectSideBar(value:{ event: { name: string; index: number } }) {
    this.subTabSelected = value.event.index;
    console.log('asdasdasdasdasdasdas');
    if(value.event.name ==='New Request'){
      this.getIndicatorList('new-requests');
    }

  }

  changeTabView(event: { event: LabelData, index: number }) {
    this.selectedTabView.set(event.event);
    this._router.navigateByUrl(event.event.route ?? '');
  }

  gotToKpiPage() {
    this._router.navigateByUrl('/dxp/visualization-wizard')
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
