import { Observable } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { options } from 'marked';

@Injectable({
  providedIn: 'root'
})
export class HttpService {
  public apiUrl: string = environment.baseUrl  + environment.apiVersion;

  constructor(
    private http: HttpClient) { }

  post(url: string, data: any = {}, prop:boolean = false): Observable<any | any[]> {
    return this.http.post((prop ?  environment.prepbaseUrl : this.apiUrl)  + url, data);
  }

  fileUpload(url: string, data: any = {}, prop:boolean = false ): Observable<any | any[]> {
    return this.http.post((prop ?  environment.prepbaseUrl : this.apiUrl) + url, data, {
      reportProgress: true,
      observe: 'events'
    } );
  }

  put(url: string, data: any = {}, prop:boolean = false): Observable<any | any[]> {
    return this.http.put((prop ?  environment.prepbaseUrl : this.apiUrl) + url, data);
  }

  patch(url: string, data: any = {}, prop:boolean = false): Observable<any | any[]> {
    return this.http.patch((prop ?  environment.prepbaseUrl : this.apiUrl) + url, data);
  }

  get(url: string, prop:boolean = false): Observable<any | any[]> {
    return this.http.get((prop ?  environment.prepbaseUrl : this.apiUrl) + url);
  }

  getSream(url: string, prop:boolean = false): Observable<any | any[]> {
    const httpHeaders: HttpHeaders = new HttpHeaders({
      // 'Content-Type': 'json/event-stream',
      'Accept': 'text/event-stream'

      // 'Accept': 'text/html, application/xhtml+xml, */*',
      // 'Content-Type': 'application/x-www-form-urlencoded'

    });
    return this.http.get((prop ?  environment.prepbaseUrl : this.apiUrl) + url, {headers: httpHeaders});
  }

  delete(url: string, prop:boolean = false): Observable<any | any[]> {
    return this.http.delete((prop ?  environment.prepbaseUrl : this.apiUrl) + url);
  }

  fileDownload(url: string): Observable<any | any[]>{
    return  this.http.get(url, {responseType: 'blob'});

  }

  fileDownloadWithHeader(url: string){
    return  this.http.get(url, {responseType: 'blob', observe: 'response'});
  }

  fileDownloadWithEvent(url: string): Observable<any | any[]>{
    return  this.http.get(url, {responseType: 'blob', reportProgress: true, observe: 'events'});

  }

  previewGet(url: string, token: string, lang: string = 'en', prop:boolean = false ): Observable<any | any[]> {
    const headers = new HttpHeaders( {
      'Authorization': `Bearer ${token}`,
      'Accept-Language': lang
    });
    return this.http.get((prop ?  environment.prepbaseUrl : this.apiUrl) + url,{headers });
  }

  postFileDownloadWithHeader(url: string, data: any = {}){
    return  this.http.post(url, data, {responseType: 'blob', observe: 'response'});
  }

  postFormData(url: string, formData: FormData, prop: boolean = false): Observable<any | any[]> {
    return this.http.post((prop ? environment.prepbaseUrl : this.apiUrl) + url, formData, {
      headers: new HttpHeaders({}),
    });
  }
}
