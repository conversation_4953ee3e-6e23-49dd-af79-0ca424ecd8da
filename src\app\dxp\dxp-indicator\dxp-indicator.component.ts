import { Component, inject, OnInit } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpWhatsNewCardComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-whats-new-card/ifp-whats-new-card.component";
import { SubSink } from 'subsink';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { dxpApi } from '../dxp.constants';
import { ListingPageData, ListingPageItem } from '../dxp.interface';
import { IfpDxpWhatsNewCardComponent } from "src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-dxp-whats-new-card/ifp-dxp-whats-new-card.component";

@Component({
  selector: 'ifp-dxp-indicator',
  imports: [TranslateModule, IfpWhatsNewCardComponent, IfpDxpWhatsNewCardComponent],
  templateUrl: './dxp-indicator.component.html',
  styleUrl: './dxp-indicator.component.scss'
})
export class DxpIndicatorComponent implements OnInit {

  private readonly _subs: SubSink = new SubSink();
  private readonly _apiService = inject(ApiService);

  public dummyCardsIds = ['6691', '5965', '2415', '2424', '5858', '2420', '2421', '2425', '2422', '5867']

  public indicatorCards: ListingPageItem[] = [];


  ngOnInit(): void {
    this.getIndicatorList();
  }

  getIndicatorList() {
    this._subs.add(
      this._apiService.getMethodRequest(dxpApi.listEntityKpi, { status: 'pending' }, true).subscribe({
        next: (resp: ListingPageData) => {
          this.indicatorCards = resp?.data ?? [];
        }
      })
    );
  }

}
