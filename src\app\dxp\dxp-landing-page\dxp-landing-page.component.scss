@use "../../../assets/ifp-styles/abstracts/index" as *;
.ifp-dxp {
  padding-bottom: $spacer-5;
  &__header-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &--main {
      margin-top: $spacer-7;
    }
    &--sub {
      margin-top: $spacer-5;
    }
  }
  &__header {
    font-size: $ifp-fs-13;
    font-weight: $fw-bold;
  }
  &__btn-wrapper {
    display: flex;
    margin: $spacer-0 (- $spacer-2);
  }
  &__btn {
    margin: $spacer-0 $spacer-2;
  }
  &__section-wrapper {
    margin-top: $spacer-5;
    display: flex;
  }
  &__desc{
    color: $ifp-color-grey-14;
    margin-top: $spacer-3;
  }

  &__left {
    min-width: 200px;
    margin-inline-end: $spacer-5;
  }
  &__right {
    width: calc(100% - 232px);
  }

  &__dropdown {
    display: block;
    margin-bottom: $spacer-4;
  }
  &__tab {
    min-width: 50%;
  }
  &--catalog {
    .ifp-dxp {
      &__left {
        display: none;
      }
      &__right {
        width: 100%;
      }
      &__desc {
        display: none;
      }
    }
  }
}

:host::ng-deep {
  .ifp-dxp {
    &__dropdown {
    .ifp-dropdown--bottom-border {
      max-width: none;
    }
    }
    &__tab {
      .ifp-category-label__link {
        display: none;
      }
    }
    &__htab {
      .ifp-h-tab {
        &__bull {
          display: none;
        }
        &__count {
          width: 20px;
          min-width: 20px;
          height: 20px;
          line-height: 20px;
          border-radius: 50%;
          text-align: center;
        }
        &__card {
          &--active,
          &:hover {
            .ifp-h-tab__count {
              color: $ifp-color-active-blue;
              background-color: $ifp-color-white-global;
            }
          }
        }
      }
    }
  }
}
