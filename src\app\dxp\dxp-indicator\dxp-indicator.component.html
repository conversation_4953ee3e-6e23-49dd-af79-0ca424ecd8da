<div class="ifp-dxp-kpis">

  <div class="ifp-dxp-kpis__section-head">
    <div class="ifp-dxp-kpis__recent">
      <p class="ifp-dxp-kpis__filter-text">{{'Recently Added' | translate}} <em class="ifp-icon ifp-icon-double-arrow ifp-db-list__filter-icon"></em></p>
    </div>
  </div>
  <div class="ifp-dxp-kpis__wrapper">
    @for (id of dummyCardsIds; let index = $index; track index) {
      <ifp-whats-new-card [classification]="'Official Statistics'"
      [id]="id"
      [title]="'Test indicator '+ index"
      [contentType]="'scad_official_indicator'"
      [index]="index+1"
      [domainId]="2234"
      [domain]="['Economy']"
      [hybridCard]="true"
      [isDxp]="true"
      [remove]="true"
      [addMyAppsLanding]="true"
      class="ifp-kpis__card"></ifp-whats-new-card>
      <!-- <ifp-dxp-whats-new-card >

      </ifp-dxp-whats-new-card> -->
    }
  </div>
</div>

