import { <PERSON>son<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ty<PERSON>, TitleCasePipe } from '@angular/common';
import { Component, ElementRef, EventEmitter, HostListener, input, Input, model, OnChanges, OnInit, Output, signal, ViewChild, WritableSignal } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { cloneDeep } from 'lodash';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpCheckBoxComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { IfpNoDataComponent } from "../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component";
import { IfpUserTagComponent } from "../../../scad-insights/ifp-widgets/ifp-atoms/ifp-user-tag/ifp-user-tag.component";

@Component({
  selector: 'ifp-panel-dropdown',
  imports: [IfpCheckBoxComponent, TranslateModule, NgClass, NgStyle, IfpTooltipDirective, ReactiveFormsModule, IfpNoDataComponent, TitleCasePipe, IfpUserTagComponent],
  templateUrl: './ifp-panel-dropdown.component.html',
  styleUrl: './ifp-panel-dropdown.component.scss'
})
export class IfpPanelDropdownComponent implements OnInit, OnChanges {
  @ViewChild('drop') drop!: ElementRef;

  @Output() multiSelected = new EventEmitter<any>();
  @Output() selected = new EventEmitter<any>();
  @Output() searchChanged = new EventEmitter<string>();

  @Input() icon!: string;
  @Input() options: PanelDropdownOptions[] = [];
  @Input() multiSelect: boolean = true;
  @Input() key!: 'key' | 'value';
  @Input() iconSize = '30px';
  @Input() selectedValue = '';
  @Input() tooltipText = '';
  @Input() label!: string;;
  @Input() multipleSelectedItems: any[] = [];
  public isBoxType = input<boolean>(false);
  public enableSearch = input(false);
  public textValue = input<string>();
  public enableSelectAll = input(false);
  public selectAll = model(false);
  public changeCheckedDataValue = input(false);
  public userDropdown = input(false);

  public showDropdown: boolean = false;
  public search = new FormControl();
  public searchOptions: WritableSignal<PanelDropdownOptions[]> = signal([]);
  ngOnInit() {
    this.multiSelectionCheck();
  }

  ngOnChanges(): void {
    this.multiSelectionCheck();
  }

 multiSelectionCheck() {
  this.multipleSelectedItems = [];
  this.options.forEach((item: PanelDropdownOptions) => {
    if (item.checked) {
      this.multipleSelectedItems.push(item);
    }
  });
  // this.search.setValue('');
  this.searchOptions.set(cloneDeep(this.options));
  this.selectAll.set(this.searchOptions().length === this.multipleSelectedItems.length);
}

  onSearch() {
    const searchResult = this.search.value.trim();
    this.searchChanged.emit(searchResult);
    const searchArray = this.options.filter((prod) => {
      return ((this.key ? prod[this.key] : prod) as string).toLowerCase().includes(searchResult.toLowerCase());
    });
    this.searchOptions.set(searchArray);
  }

  @HostListener('window:pointerup', ['$event.target'])
  onOutsideClick(target: HTMLElement) {
    if (this.drop) {
      if (!this.drop.nativeElement.contains(target)) {
        this.showDropdown = false;
        // this.search.setValue('');
        // this.searchOptions.set([]);
        // this.searchChanged.emit('');
      }
    }
  }

  selectAllValues() {
    this.selectAll.set(!this.selectAll());
    this.multipleSelectedItems = [];
    if (this.selectAll()) {
      this.options.forEach(data => {
        this.multipleSelectedItems.push(data);
        data.checked = true;
      });
    } else {
      this.options.forEach(data => data.checked = false);
    }
    this.onSearch();
    this.multiSelected.emit(this.multipleSelectedItems);
  }


  setChecked(event: boolean, item: any) {
    if (this.multipleSelectedItems?.length == 1 && !event) {
      return;
    }
    item.checked = event;
    if (event) {
      this.multipleSelectedItems.unshift(item);
      this.selectAll.set(this.multipleSelectedItems.length === this.searchOptions().length);
    } else {
      const index = this.multipleSelectedItems.findIndex((x: PanelDropdownOptions) => x.value == item.value);
      if (index >= 0) {
        this.multipleSelectedItems.splice(index, 1);
      }
      this.selectAll.set(false);
    }
    this.multiSelected.emit(this.multipleSelectedItems);
  }

  getMutiselectedItem(item: Record<string, any>) {
    let isSelected: boolean = false;
    if (this.multipleSelectedItems?.find((x: Record<string, any>) => x[this.key] == item[this.key])) {
      isSelected = true;
    }
    return isSelected;
  }

  setSingleSelection(item: PanelDropdownOptions) {
    if (item.key === this.selectedValue) {
      this.selected.emit(undefined);
      this.selectedValue = '';
    } else {
      this.selected.emit(item);
      this.selectedValue = item.key;
    }


  }

  checkDisable() {
    let isDisable: boolean = false;
    if (this.searchOptions().filter(x => x.checked).length === 1) {
      isDisable = true;
    }
    return isDisable;
  }


}

export interface PanelDropdownOptions {
  key: string;
  value: string;
  id?: string;
  shortValue?: string;
  checked: boolean;
}
